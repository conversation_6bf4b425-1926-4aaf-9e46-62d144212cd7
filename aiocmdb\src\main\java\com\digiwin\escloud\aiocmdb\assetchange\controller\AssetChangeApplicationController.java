package com.digiwin.escloud.aiocmdb.assetchange.controller;

import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.service.IAssetChangeApplicationService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 资产变更申请单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Api(tags = "资产变更申请单管理")
@RestController
@RequestMapping("/asset/assetChangeApplication")
public class AssetChangeApplicationController {

    @Autowired
    private IAssetChangeApplicationService assetChangeApplicationService;

    @ApiOperation(value = "新增/保存资产变更申请单")
    @PostMapping("/save")
    public ResponseBase saveAssetChangeApplication(@RequestBody AssetChangeApplicationSaveRequest request) {
        return assetChangeApplicationService.saveAssetChangeApplication(request);
    }

    @ApiOperation(value = "删除资产变更申请单")
    @DeleteMapping("/delete/{id}")
    public ResponseBase deleteAssetChangeApplication(@PathVariable String id) {
        return assetChangeApplicationService.deleteAssetChangeApplication(id);
    }

    @ApiOperation(value = "根据ID查询资产变更申请单详情")
    @GetMapping("/detail/{applicationId}")
    public ResponseBase getAssetChangeApplicationDetail(@PathVariable Long applicationId) {
        return assetChangeApplicationService.getAssetChangeApplicationDetail(applicationId);
    }

    @ApiOperation(value = "生成申请单编号")
    @GetMapping("/generateNumber/{eid}")
    public ResponseBase generateApplicationNumber(@PathVariable Long eid) {
        try {
            String applicationNumber = assetChangeApplicationService.generateApplicationNumber(eid);
            return ResponseBase.ok(applicationNumber);
        } catch (Exception e) {
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR,"生成申请单编号失败: " + e.getMessage());
        }
    }
}
