package com.digiwin.escloud.aiocmdb.assetchange.service;

import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BaseChangeRequestState implements ChangeRequestState{

    // 由Spring注入
    protected StateFactory stateFactory;

    @Autowired
    public void setStateFactory(StateFactory stateFactory) {
        this.stateFactory = stateFactory;
    }
    protected void unsupportedOp() {
        System.err.println("错误: 在 [" + getStateName() + "] 状态下不支持此操作!");
    }

    @Override
    public void submitApproval(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void approvePass(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void approveFail(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void approveAdjust(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void execute(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void submitAcceptance(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void acceptPass(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void acceptFail(AssetChangeApplication context) {
        unsupportedOp();
    }

    @Override
    public void acceptAdjust(AssetChangeApplication context) {
        unsupportedOp();
    }
}
