package com.digiwin.escloud.aiouser.model.common;

import com.digiwin.escloud.aiocmdb.model.assetchange.AssetChangeApplication;
import com.digiwin.escloud.aiouser.model.supplier.SupplierEmployee;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class Invitation {
    private long id;
    private String inviterName;
    private Long inviterUserSid;
    private Long invitedEid;
    private String invitedTenantId;
    private String invitedTenantName;
    private String invitedTaxCode;
    private Long invitedEmployeeId;
    private Date invitationDate;
    private Long invitedSid;
    private String inviteType;
    private String invitedServiceCode;
    private String invitedName;
    private String invitedEmail;
    private String invitedPhone;
    private String linkUrl;
    private String invitedUserId;
    private boolean newUser;
    private boolean activated;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastNoticeTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private SupplierEmployee supplierEmployee;
    /**
     * 资产变更申请单的id
     */
    private AssetChangeApplication application;
    public Invitation(){

    }

    public Invitation(long id, String inviterName, Long invitedEid,
                      String invitedTenantName, Long invitedEmployeeId, String linkUrl) {
        this.id = id;
        this.inviterName = inviterName;
        this.invitedEid = invitedEid;
        this.invitedTenantName = invitedTenantName;
        this.invitedEmployeeId = invitedEmployeeId;
        this.invitationDate = new Date();
        this.linkUrl = linkUrl;
    }
}
