<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationMapper">

    <!-- 插入资产变更申请单 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication">
        INSERT INTO asset_change_application (
            id, eid, applicationNumber, applicationDate, applicantUserId, applicantName,
            applicantUnit, applicationCategory, changeRange,applicationStatus, latestApprovalComment,
            latestAcceptanceComment, changeBackgroundReason, changeContentDescription,
            changePriority, riskAssessment, rollbackPlan, createTime, updateTime
        ) VALUES (
            #{id}, #{eid}, #{applicationNumber}, #{applicationDate}, #{applicantUserId}, #{applicantName},
            #{applicantUnit}, #{applicationCategory},#{changeRange}, #{applicationStatus}, #{latestApprovalComment},
            #{latestAcceptanceComment}, #{changeBackgroundReason}, #{changeContentDescription},
            #{changePriority}, #{riskAssessment}, #{rollbackPlan}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新资产变更申请单 -->
    <update id="updateById" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication">
        UPDATE asset_change_application SET
            eid = #{eid},
            applicationNumber = #{applicationNumber},
            applicationDate = #{applicationDate},
            applicantUserId = #{applicantUserId},
            applicantName = #{applicantName},
            applicantUnit = #{applicantUnit},
            changeRange = #{changeRange},
            applicationCategory = #{applicationCategory},
            applicationStatus = #{applicationStatus},
            latestApprovalComment = #{latestApprovalComment},
            latestAcceptanceComment = #{latestAcceptanceComment},
            changeBackgroundReason = #{changeBackgroundReason},
            changeContentDescription = #{changeContentDescription},
            changePriority = #{changePriority},
            riskAssessment = #{riskAssessment},
            rollbackPlan = #{rollbackPlan},
            updateTime = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询资产变更申请单 -->
    <select id="selectById" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication">
        SELECT * FROM asset_change_application WHERE id = #{id}
    </select>

    <!-- 根据申请单编号查询资产变更申请单 -->
    <select id="selectByApplicationNumber" parameterType="string" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication">
        SELECT * FROM asset_change_application WHERE applicationNumber = #{applicationNumber}
    </select>

    <!-- 检查申请单编号是否存在 -->
    <select id="checkApplicationNumberExists" resultType="int">
        SELECT COUNT(1) FROM asset_change_application
        WHERE applicationNumber = #{applicationNumber}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据ID删除资产变更申请单 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM asset_change_application WHERE id = #{id}
    </delete>

    <!-- 查询指定eid和日期下的最大流水号 -->
    <select id="getMaxSequenceByEidAndDate" resultType="int">
        SELECT COALESCE(MAX(CAST(SUBSTRING(applicationNumber, -3) AS UNSIGNED)), 0)
        FROM asset_change_application
        WHERE eid = #{eid}
        AND applicationNumber LIKE CONCAT('CR-', #{dateStr}, '-%')
    </select>

    <!-- 查询指定eid和日期下的最大流水号 -->
    <select id="getMaxSequenceByEid" resultType="int">
        SELECT COALESCE(MAX(CAST(SUBSTRING(applicationNumber, -3) AS UNSIGNED)), 0)
        FROM asset_change_application
        WHERE eid = #{eid}
    </select>

</mapper>
