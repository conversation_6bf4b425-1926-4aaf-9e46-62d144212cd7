package com.digiwin.escloud.aiocmdb.assetchange.service;

import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeApplicationSaveRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeOperationRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 资产变更申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface IAssetChangeApplicationService {

    /**
     * 新增/保存资产变更申请单
     * 在一个事务中保存主表及所有子表信息
     *
     * @param request 保存请求
     * @return 响应结果
     */
    ResponseBase saveAssetChangeApplication(AssetChangeApplicationSaveRequest request);

    /**
     * 删除资产变更申请单
     *
     * @param id 申请单id
     * @return 响应结果
     */
    ResponseBase deleteAssetChangeApplication(String id);

    /**
     * 根据申请单ID查询完整的申请单详情
     * 包括主表信息、变更资产清单、执行计划、执行记录、操作记录、停机状况
     *
     * @param applicationId 申请单ID
     * @return 响应结果
     */
    ResponseBase getAssetChangeApplicationDetail(Long applicationId);

    ResponseBase submitForApproval(AssetChangeOperationRequest request);

    ResponseBase approveApplication(AssetChangeOperationRequest request);

    ResponseBase adjustApplication(AssetChangeOperationRequest request);

    ResponseBase approveFailApplication(AssetChangeOperationRequest request);

    ResponseBase executeApplication(AssetChangeOperationRequest request);

    ResponseBase submitAcceptanceApplication(AssetChangeOperationRequest request);

    ResponseBase acceptApplication(AssetChangeOperationRequest request);

    ResponseBase acceptAdjustApplication(AssetChangeOperationRequest request);

    ResponseBase acceptFailApplication(AssetChangeOperationRequest request);

    /**
     * 生成申请单编号
     * 格式：CR-YYYYMMDD-流水号，示例：CR-20250401-001
     *
     * @param eid 企业ID
     * @return 申请单编号
     */
    String generateApplicationNumber(Long eid);
}
