package com.digiwin.escloud.aiouser.service.iamapp.impl.user;

import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.dao.IUserDao;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.common.InviteType;
import com.digiwin.escloud.aiouser.model.user.CountingUser;
import com.digiwin.escloud.aiouser.util.MailUtils;
import com.digiwin.escloud.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2025-08-13 11:34
 * @Description
 */
@Slf4j
@Service("ACTIVATE_APPROVER")
public class ActiveApproverService extends ActivateUserService {

    @Resource
    private IUserDao userDao;
    @Resource
    private MailUtils mailUtils;

    @Override
    protected Invitation buildInvitation(CountingUser countingUser, String inviterName, Long inviterUserSid) {
        Invitation invitation = super.buildInvitation(countingUser, inviterName, inviterUserSid);
        invitation.setInviteType(InviteType.ACTIVATE_ASSET_APPROVER.toString());
        HashMap<String, Object> map = new HashMap<>();
        map.put("invitedUserId", countingUser.getUserId());
        map.put("inviteType", "ACTIVATE_ASSET");
        Invitation lastInvitation = userDao.getLastInvitationByUserInviteType(map);
        if (lastInvitation != null) {
            invitation.setNewUser(lastInvitation.isNewUser());
        } else {
            invitation.setNewUser(countingUser.isNewUser());
        }
        return invitation;
    }

    @Override
    protected void sendMail(Invitation invitation) {
        //todo 邮件type改成验收人激活邮件
        mailUtils.sendMail(MailSourceType.ActivateUser.toString(), invitation);
    }

    @Override
    protected BaseResponse otherCheck(List<CountingUser> countingUsers, BaseResponse response) {
        return response;
    }
}
