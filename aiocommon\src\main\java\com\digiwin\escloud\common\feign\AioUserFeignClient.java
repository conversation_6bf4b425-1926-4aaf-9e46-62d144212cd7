package com.digiwin.escloud.common.feign;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiouser.model.customer.UserRelatedName;
import com.digiwin.escloud.aiouser.model.customer.UserRelatedNameListResponse;
import com.digiwin.escloud.aiouser.model.notify.UserNotifySetting;
import com.digiwin.escloud.aiouser.model.omc.OmcOrder;
import com.digiwin.escloud.aiouser.model.supplier.*;
import com.digiwin.escloud.aiouser.model.tenant.*;
import com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroupRespDTO;
import com.digiwin.escloud.aiouser.model.user.*;
import com.digiwin.escloud.aiouser.model.usercontact.UserContact;
import com.digiwin.escloud.common.constant.AioConstant;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.TenantTpParams;
import com.digiwin.escloud.common.model.tenant.ServiceStaffRequest;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;


@FeignClient(value = "aiouser", url = "${feign.aiouser.url:}")
public interface AioUserFeignClient {

    @GetMapping("/api/tenant/getTenantContract")
    TenantContract getTenantContract(@RequestParam("sid") long sid, @RequestParam("eid") long eid, @RequestParam("productCode") String productCode);

    @GetMapping("/api/supplier/getSupplierName")
    String getSupplierName(@RequestParam("sid") long sid);

    @GetMapping("/api/user/getUserPersonalInfo")
    UserPersonInfo getUserPersonalInfo(@RequestParam("userSid") long userSid);

    @GetMapping("/api/user/getSupplierEmployeeInfo")
    SupplierEmployeeInfo getSupplierEmployeeInfo(@RequestParam("sid") long sid, @RequestParam("userSid") long userSid);

    @PostMapping("/api/user/getModuleStaff")
    BaseResponse<List<User>> getModuleStaff(@RequestBody ServiceStaffRequest serviceCodeList);

    @PostMapping("/api/usercontact/getUserContractId")
    long getUserContractId(@RequestBody UserContact userContact);

    @GetMapping("/api/product/getClassificationId")
    long getClassificationId(@RequestParam("sid") long sid, @RequestParam("productCode") String productCode, @RequestParam("classificationCode") String classificationCode);

    @GetMapping("/api/product/getModuleId")
    long getModuleId(@RequestParam("sid") long sid, @RequestParam("productCode") String productCode, @RequestParam("moduleCode") String moduleCode);

    @GetMapping("/api/usercontact/{sid}/{serviceCode}/{productCode}/new/staff")
    ServiceStaffResponse getServiceStaffInfo(
            @PathVariable(value = "sid") long sid,
            @PathVariable(value = "serviceCode") String serviceCode,
            @PathVariable(value = "productCode") String productCode,
            @RequestParam(value = "serviceGroupCategory", defaultValue = "", required = false) String serviceGroupCategory,
            @RequestParam(value = "serviceRegion", defaultValue = "", required = false) String serviceRegion,
            @RequestParam(value = "serviceLanguage", defaultValue = "", required = false) String serviceLanguage,
            @RequestParam(value = "issueClassificationNo", defaultValue = "", required = false) String issueClassificationNo,
            @RequestParam(value = "submitWay", defaultValue = "", required = false) String submitWay,
            @RequestParam(value = "module", defaultValue = "", required = false) String module,
            @RequestParam Map<String, String> issueContentMap);

    @PostMapping("/api/tenant/name/list")
    TenantNameListResponse getTenantNameList(@RequestBody() Collection<Long> tenantIdList);

    @PostMapping(value = "/api/tenant/tenantServiceCodeGet")
    TenantNameListResponse tenantServiceCodeGet(@RequestBody Collection<Long> tenantIdList);

    @RequestMapping(
            value = {"/api/supplier/basic/create/incoming/info"},
            method = {RequestMethod.GET}
    )
    SupplierCreateIncomingRes getSupplierCreateIncomingInfo();

    //查询客户相关信息
    @RequestMapping(
            value = {"/customer/user/related/name/list"},
            method = {RequestMethod.POST}
    )
    UserRelatedNameListResponse getUserRelatedNameList(@RequestBody() List<UserRelatedName> userRelatedNameList);

    @RequestMapping(value = {"/api/user/roles"}, method = {RequestMethod.GET})
    UserRolesGetResponse getUserRoles(@RequestParam("authUserSid") long authUserSid,
                                      @RequestHeader(AioConstant.TOKEN) String token);

    @RequestMapping(value = {"/api/user/token/info"}, method = {RequestMethod.POST})
    String getUserInfoJsonByToken(@RequestHeader(AioConstant.TOKEN) String token);

    @GetMapping("/api/usercontact/noticeGroupMail")
    ResponseBase<List<Map<String, Object>>> getNoticeGroupMail(
            @ApiParam(value = "收集项编码", required = true)
            @RequestParam("collectCode") String collectCode,
            @ApiParam(value = "预警项编码", required = true)
            @RequestParam("warningCode") String warningCode,
            @ApiParam(value = "预警级别", required = true)
            @RequestParam("warningLevel") String warningLevel,
            @ApiParam(value = "设备ID", required = true)
            @RequestParam("warningDevice") String warningDevice,
            @ApiParam(value = "设备类型", required = true)
            @RequestParam("warningDeviceType") String warningDeviceType,
            @ApiParam(value = "客服代号(租户代号)", required = true)
            @RequestParam("serviceCode") String serviceCode);

    @ApiOperation(value = "查询通知用户")
    @PostMapping("/tenant/notice/group/userNoticeContact")
    ResponseBase getUserNoticeContact(@RequestBody List<Long> tngIdList);

    @ApiOperation(value = "查询通知用户")
    @PostMapping("/tenant/notice/group/userNoticeContactByMail")
    ResponseBase getUserNoticeContactByMail(@RequestParam(value = "eid", required = false, defaultValue = "") String eid,
                                            @RequestBody List<String> mailList);

    @ApiOperation(value = "列表分页查询")
    @GetMapping("/tenant/notice/group/list")
    ResponseBase getTenantNoticeGroupList(@RequestParam Map<String, Object> params);

    @ApiOperation(value = "获取运营商产品设定")
    @GetMapping(value = "/api/supplier/im/product/idle/setting")
    SupplierProductSettingResponse getImProductIdleSetting(
            @RequestHeader("sid") Long sid,
            @RequestParam(value = "productCode") String productCode);

    @ApiOperation(value = "查询租户Id存在性")
    @GetMapping(value = "/api/tenant/exist")
    ResponseBase checkTenantExist(@RequestHeader(AioConstant.EID) Long eid);

    @ApiOperation(value = "获取特定产品信息")
    @RequestMapping(value = "/api/product/{spId}/info/by/id", method = RequestMethod.GET)
    BaseResponse getProductInfoBySpId(@ApiParam(value = "产品主键", required = true)
                                      @PathVariable(value = "spId") Long spId);

    @ApiOperation(value = "获取用户关注的租户")
    @GetMapping("/api/user/tenant/attention")
    ResponseBase getTenantAndAttention(@RequestParam("orgSid") Long orgSid, @RequestParam("userSid") Long userSid, @RequestParam("sid") Long sid);

    @ApiOperation(value = "获取用户部门信息")
    @GetMapping("/api/user/org")
    ResponseBase getUserOrg(@RequestParam("needAllOrg") Boolean needAllOrg,
                            @RequestHeader(AioConstant.SID) Long sid,
                            @RequestHeader(AioConstant.EID) Long eid,
                            @RequestHeader(AioConstant.CONTEXT_KEY_USER_SID) Long userSid);


    @ApiOperation(value = "通过eid获取关注的用户")
    @GetMapping("/api/user/{eid}/attention")
    ResponseBase getAttentionUserByEid(@PathVariable("eid") String eid, @RequestParam(value = "receiveWarningMail", required = false) Boolean receiveWarningMail);

    @ApiOperation(value = "依据运维项目列表获取运维商运维模组类别明细列表")
    @PostMapping("/supplier/aiops/module/class/detail/by/aiops/item/list")
    BaseResponse getModuleClassDetailByAiopsItemList(
            @RequestParam(value = "aiopsItemType", required = false, defaultValue = "") String aiopsItmeType,
            @RequestBody(required = false) List<String> aiopsItemList);

    @ApiOperation(value = "依据运维项目获取租户模组合约与类别明细")
    @PostMapping(value = "/api/tenant/module/contract/class/by/aiops/item/list")
    BaseResponse<List<TenantModuleContractDetail>> getTenantModuleContractClassDetailByAiopsItemList(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(required = true, value = "租户Id")
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "运维项目列表")
            @RequestBody(required = false) Collection<String> aiopsItem);

    @ApiOperation(value = "依据租户模组合约明细更新已用总数")
    @PutMapping(value = "/api/tenant/module/contract/detail/used/count/by/tmcd/list")
    BaseResponse<List<TenantModuleContractDetail>> modifyTenantModuleContractDetailUsedCountByTmcdList(
            @ApiParam(required = true, value = "租户Id")
            @RequestParam(value = "eid") Long eid,
            @ApiParam(required = true, value = "租户模组合约明细列表")
            @RequestBody() Collection<TenantModuleContractDetail> tmcdList);

    @ApiOperation(value = "依据运维商运维模组收集项分层Id列表获取运维商模组收集项分层列表")
    @PostMapping(value = "/supplier/aiops/module/collect/layered/by/samcl/id/list")
    BaseResponse<List<SupplierAiopsModuleCollectLayered>> getSupplierAiopsModuleClassDetailBySamclIdList(
            @ApiParam(value = "运维商运维模组收集项分层列表", required = true)
            @RequestBody(required = false) Collection<Long> samclIdList);

    @ApiOperation(value = "获取租户Id有效的运维商运维模组类别明细Id列表")
    @PostMapping(value = "/api/tenant/{eid}/valid/samcd/id/list")
    BaseResponse<List<Long>> getTenantValidSamcdIdList(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "运维商运维模组类别明细Id列表", required = true)
            @PathVariable("eid") Long eid,
            @ApiParam(value = "运维项目列表")
            @RequestBody(required = false) Collection<String> aiopsItemList);

    @ApiOperation(value = "重新计算租户模组合约明细已用数量")
    @PutMapping(value = "/api/tenant/{eid}/recalculate/tmcd/used/count")
    BaseResponse recalculateTmcdUsedCount(
            @ApiParam(value = "租戶Id", required = true)
            @PathVariable("eid") Long eid,
            @ApiParam(value = "更新时是否限制已用数量不能大于可用数量", required = true)
            @RequestParam(value = "isLimited", required = false, defaultValue = "true") Boolean isLimited,
            @ApiParam(value = "运维项目列表")
            @RequestBody(required = false) List<String> aiopsItemList);

    @ApiOperation(value = "依据授权已用请求重新计算租户模组合约明细已用数量")
    @PutMapping(value = "/api/tenant/recalculate/tmcd/used/count/by/tmcd/id/map")
    BaseResponse recalculateTmcdUsedCountByAuthUsedRequest(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "授权已用请求", required = true)
            @RequestBody() AuthUsedRequest authUsedRequest);

    @ApiOperation(value = "依据用户Sid列表查询用户名称字典")
    @PostMapping("/api/user/name/by/sid/list")
    BaseResponse<Map<Long, String>> getUserNameBySidList(
            @ApiParam(required = true, value = "用户Sid列表")
            @RequestBody Collection<Long> userSidList);

    @ApiOperation(value = "依据产品Id列表获取产品代号字典")
    @PostMapping(value = "/api/product/code/map/by/id/list")
    BaseResponse<Map<Long, String>> getProductCodeMapByIdList(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "产品Id列表", required = true)
            @RequestBody() Collection<Long> spIdList);

    @ApiOperation(value = "依据运维商运维模组类别明细Id列表获取运维商运维模组类别明细列表")
    @GetMapping(value = "/supplier/aiops/module/class/detail/by/aiops/item/type")
    BaseResponse<List<SupplierAiopsModuleClassDetail>> getSupplierAiopsModuleClassDetailByAiopsItemType(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "运维商运维模组类别明细Id列表", required = true)
            @RequestParam("aiopsItemType") String aiopsItemType);

    @ApiOperation(value = "依据产品代号获取产品Id")
    @GetMapping(value = "/api/product/id/by/code")
    BaseResponse<Long> getProductIdByCode(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "产品代号", required = true)
            @RequestParam("productCode") String productCode);


    @ApiOperation(value = "根据客代查询租户")
    @PostMapping("/api/tenant/supplierTenantMapListByServiceCodeList")
    ResponseBase getSupplierTenantMapListByServiceCodeList(@RequestBody Collection<String> serviceCodeList);

    @ApiOperation(value = "根据客代查询租户")
    @PostMapping("/api/tenant/getSupplierTenantMapListByEidList")
    ResponseBase getSupplierTenantMapListByEidList(@RequestBody Collection<Long> eidList);

    @ApiOperation(value = "获取特定租戶下所有用户通知设定")
    @GetMapping("/user/notify/setting/by/tenant")
    BaseResponse<List<UserNotifySetting>> getUserNotifySettingByTenant(
            @RequestHeader(AioConstant.SID) Long sid,
            @ApiParam(value = "租户Sid", required = true)
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "目标类型", required = true)
            @RequestParam(value = "targetType") String targetType,
            @ApiParam(value = "目标项目")
            @RequestParam(value = "targetItem", required = false) String targetItem,
            @ApiParam(value = "启用状态")
            @RequestParam(value = "isEnable", required = false) Boolean isEnable,
            @ApiParam(value = "应用编号")
            @RequestParam(value = "appCode", required = false) String appCode);

    @ApiOperation(value = "移转驻派员合约设定")
    @PutMapping(value = "/transfer/setting/esclient/contract")
    BaseResponse transferEsclientContractSetting(
            @ApiParam(value = "是否自动检查并添加租户")
            @RequestParam(value = "autoAddTenant", required = false, defaultValue = "true") Boolean autoAddTenant,
            @ApiParam(value = "是否强制更新租户模组合约")
            @RequestParam(value = "forceUpdateTmc", required = false, defaultValue = "false") Boolean forceUpdateTmc,
            @ApiParam(value = "起始页次")
            @RequestParam(value = "startPage", required = false) Integer startPage,
            @ApiParam(value = "客服代号列表")
            @RequestBody(required = false) List<String> serviceCodeList);

    @ApiOperation(value = "查看移转驻派员合约设定任務狀態")
    @PostMapping(value = "/transfer/setting/esclient/contract/watch")
    BaseResponse watchTransferEsclientContractSettingTask(
            @ApiParam(value = "状态序号")
            @RequestParam(value = "statusIndex", required = false) String statusIndex,
            @ApiParam(value = "客服代号列表")
            @RequestBody(required = false) List<String> serviceCodeList);

    @ApiOperation(value = "依据客服代号获取默认通知用户")
    @GetMapping("/tenant/notice/group/userNoticeContactByServiceCode")
    BaseResponse<List<TenantNotifyGroupRespDTO>> getUserNoticeContactByServiceCode(
            @ApiParam(value = "客服代号或租户Sid", required = true)
            @RequestParam(value = "serviceCodeOrEid") String serviceCodeOrEid,
            @ApiParam(value = "是否是默认")
            @RequestParam(value = "isDefault", required = false, defaultValue = "true") Boolean isDefault);

    @ApiOperation(value = "依据运维商运维模组类别明细Id列表获取合并统计明细")
    @PostMapping(value = "/supplier/aiops/merge/statistics/detail/by/samcd/id/list")
    BaseResponse<List<Map<String, Object>>> getMergeStatisticsDetailBySamcdIdList(
            @ApiParam(value = "运维商运维模组类别明细Id列表", required = true)
            @RequestBody() Collection<Long> samcdIdList);

    @ApiOperation(value = "依据运维商模组类别类别代号获取运维商模组类别明细Id列表")
    @GetMapping(value = "/supplier/aiops/model/detail/id/by/classCode")
    BaseResponse<List<Long>> getSamcdIdListByClassCode(
            @ApiParam(value = "运维商模组类别类别代号", required = true)
            @RequestParam("classCode") String classCode);

    @ApiOperation(value = "获取tmcdIdList")
    @PostMapping(value = "/api/tenant/getTmcdListByTmcIdList")
    ResponseBase<List<TenantModuleContractDetail>> getTmcdListByTmcIdList(
            @RequestBody() List<Long> tmcIdList);

    @ApiOperation(value = "修复tmcdId use count")
    @PostMapping(value = "/api/tenant/fixTmcdUseCount")
    ResponseBase fixTmcdUseCount(
            @RequestBody() List<Map<String, Object>> tmcdUseCountList);

    @ApiOperation(value = "依据条件字典获取运维商运维模组类别明细列表")
    @PostMapping(value = "/supplier/aiops/model/detail/by/map")
    BaseResponse<List<SupplierAiopsModuleClassDetail>> getSamcdListByMap(
            @ApiParam(value = "条件字典", required = true)
            @RequestBody() Map<String, Object> map);

    @ApiOperation(value = "将亚信的租户与鼎捷租户mapping")
    @PostMapping(value = "/asia/info/user/insert/tenantTp")
    BaseResponse insertOrUpdateTenantTp(TenantTpDto tt);

    @ApiOperation(value = "获取亚信租户与鼎捷租户数据")
    @GetMapping(value = "/asia/info/user/tenantTp")
    BaseResponse<TenantTpDto> getTenantTp(@RequestParam(value = "tpTenantName") String tpTenantName,
                                          @RequestParam(value = "eid") Long eid);

    @ApiOperation(value = "获取亚信租户与鼎捷租户数据")
    @PostMapping(value = "/asia/info/user/getAsiaInfoUser")
    BaseResponse getAsiaInfoUser(@RequestBody TenantTpParams ttp);

    @ApiOperation(value = "获取全部亚信租户与鼎捷租户数据")
    @GetMapping(value = "/asia/info/user/tenantTp/list")
    BaseResponse<List<TenantTpDto>> getTenantTpList();

    @PostMapping("/api/iamuser/getSupplierAiopsModuleV2")
    BaseResponse getSupplierAiopsModule(@RequestParam("serviceProviderSid") Long serviceProviderSid, @RequestParam("sid") Long sid,@RequestBody List<Long> eidList);

    @GetMapping("/api/iamuser/getModuleTagData")
    BaseResponse getModuleTagData(@RequestParam("moduleCode") String moduleCode);

    @ApiOperation(value = "获取授权应用")
    @GetMapping("/api/oc/get/order")
    ResponseBase getOrder(@ApiParam(value = "客服代号", required = true)
                          @RequestParam("serviceCode") String serviceCode,
                          @ApiParam(value = "应用Id")
                          @RequestParam(value = "appId", required = false) String appId,
                          @ApiParam(value = "应用代号")
                          @RequestParam(value = "requestToken", required = false) String requestToken);

    @ApiOperation(value = "dap人工授权应用")
    @PostMapping("/api/oc/pre/order")
    ResponseBase preOrder(@RequestHeader(AioConstant.TOKEN) String token,
                          @RequestHeader(AioConstant.FUNC_APP_ID) String appId,
                          @RequestBody OmcOrder omcOrder);

    @ApiOperation(value = "获取数据权限角色")
    @GetMapping("/api/iamuser/getIamDataAuthRoleList")
    ResponseBase<List<PermissionRole>> getIamDataAuthRoleList(@RequestParam("token") String token, @RequestParam("userSid") Long userSid
            , @RequestParam("eid") Long eid
            , @RequestParam("sid") Long sid
    );

    @ApiOperation(value = "获取角色详情")
    @PostMapping("/api/role/getDataAuthRoleDetailList")
    ResponseBase<List<PermissionRole>> getDataAuthRoleDetailList(@RequestParam("eid")Long eid,
                                                  @RequestBody List<String> roleCodeList , @RequestParam("sid") Long sid);

    @ApiOperation(value = "获取指定角色的部门情况")
    @GetMapping("/api/user/getCurrentDept")
    ResponseBase<String> getCurrentDept(@RequestParam(value = "userSid", required = false) Long userSid,
                                @RequestParam("eid") Long eid, @RequestParam("dynamicValueCode") String dynamicValueCode
            , @RequestParam("userId") String userId);

    @ApiOperation(value = "依据运维项目获取租户模组合约与类别明细,上下级曾现")
    @PostMapping(value = "/api/tenant/module/contract/class/by/aiops/item/list/v2")
    BaseResponse getTenantModuleContractClassDetailByAiopsItemListV2(
            @RequestHeader("sid") Long sid,
            @ApiParam(required = true, value = "租户Id")
            @RequestParam(value = "eid") Long eid,
            @ApiParam(value = "运维项目列表")
            @RequestBody(required = false) List<String> aiopsItem);

    // 10845【后台api】数据服务的报表模板可以在智管家呈现给客户 -平台菜单和运维模组或者运维项关联
    @ApiOperation(value = "获取租户已购买模组")
    @GetMapping(value = "/api/tenant/module/hold/auth/contracts/v2")
    ResponseBase<List<TenantModuleContract>> getTenantHoldAuthModulesV2(
            @RequestHeader("sid") Long sid,
            @RequestHeader("eid") Long eid);

    @ApiOperation(value = "获取租户已购买模组(有 samcd.holdAuth = 1)")
    @GetMapping(value = "/api/tenant/module/hold/auth/contracts")
    ResponseBase<List<TenantModuleContract>> getTenantHoldAuthModules(
            @RequestHeader("sid") Long sid,
            @RequestHeader("eid") Long eid);

    @PostMapping("/api/user/sendMailAndProcessOther")
    BaseResponse sendMailAndProcessOther(@RequestBody ProductLine147NoticeMailParams mailParams);

    @PostMapping("/api/iamuser/es/identity/login")
    JSONObject doEsIamUserLogin(@ApiParam(required = true, value = "登录平台") @RequestParam(value = "aioPlatformEnum", required = true) AioPlatformEnum aioPlatformEnum,
                                @ApiParam(required = true, value = "用户id") @RequestParam(value = "userId", required = true) String userId,
                                @ApiParam(required = true, value = "客代") @RequestParam(value = "serviceCode", required = true) String serviceCode);

    @GetMapping(value = "/api/tenant/getAuthTmc")
    ResponseBase<List<String>> getAuthTmc(@RequestParam("eid") Long eid);

    @ApiOperation(value = "获取某租户模组合约列表")
    @GetMapping(value = "/api/tenant/module/contracts/v2")
    ResponseBase<List<TenantModuleContract>> getTenantModuleContractsByServiceCodeV2(
            @RequestHeader("sid") Long sid,
            @RequestParam(value = "eid", required = false) Long eid
    );
    @ApiOperation(value = "查詢租戶(分頁)")
    @GetMapping(value = "/api/tenant/searchTenantList")
    ResponseBase searchTenantList(@ApiParam(required = false, value = "搜尋的租戶資訊-eid、客服代號、租戶名稱") @RequestParam(value = "tenantSearchContent", required = false, defaultValue = "") String tenantSearchContent,
                                         @ApiParam(required = false, value = "是否需要权限") @RequestParam(value = "needAuth", required = false, defaultValue = "true") boolean needAuth,
                                         @ApiParam(required = false, value = "产品代号") @RequestParam(value = "productCode", required = false, defaultValue = "") String productCode,
                                         @ApiParam(required = false, value = "頁碼") @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                                         @ApiParam(required = false, value = "筆數") @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize);

    @GetMapping("/api/supplier/{sid}")
    BaseResponse<Supplier> getSupplier(@PathVariable(value = "sid") long sid);


    @ApiOperation(value = "用户注册开通授权 目前给资产变更单审批和验收使用")
    @PostMapping("/api/iamuser/app/authorized")
    BaseResponse doIamUserAuthorized(@RequestParam(value = "addUserScene") String addUserScene,
                                            @RequestBody() List<CountingUser> countingUsers);
}