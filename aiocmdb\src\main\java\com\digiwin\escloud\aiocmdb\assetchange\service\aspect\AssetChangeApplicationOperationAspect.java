package com.digiwin.escloud.aiocmdb.assetchange.service.aspect;

import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationMapper;
import com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationOperationRecordMapper;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplication;
import com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationOperationRecord;
import com.digiwin.escloud.aiocmdb.assetchange.model.dto.AssetChangeOperationRequest;
import com.digiwin.escloud.aiocmdb.assetchange.model.enums.OperationType;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Aspect
@Component
@Slf4j
public class AssetChangeApplicationOperationAspect {

    @Autowired
    private AssetChangeApplicationMapper applicationMapper;

    @Autowired
    private AssetChangeApplicationOperationRecordMapper logMapper;

    // 只拦截那几个核心的审批、执行、验收方法
    @Around("execution(* com.digiwin.escloud.aiocmdb.assetchange.service.impl.AssetChangeApplicationServiceImpl.approve*(..)) || " +
            "execution(* com.digiwin.escloud.aiocmdb.assetchange.service.impl.AssetChangeApplicationServiceImpl.adjust*(..)) || " +
            "execution(* com.digiwin.escloud.aiocmdb.assetchange.service.impl.AssetChangeApplicationServiceImpl.execute*(..)) ||" +
            "execution(* com.digiwin.escloud.aiocmdb.assetchange.service.impl.AssetChangeApplicationServiceImpl.accept*(..))")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {

        // 1. 从方法参数中获取申请单ID和备注
        Object[] args = joinPoint.getArgs();
        AssetChangeOperationRequest request = (AssetChangeOperationRequest) args[0];

        Long applicationId = request.getApplicationId();
        String operatorUserId = request.getOperatorUserId();
        String operatorName = request.getOperatorName();
        String operationExplanation = request.getOperationExplanation();
        String operationOpinion = request.getOperationOpinion();
        String nextUserName = request.getNextUserName();
        String nextUserId = request.getNextUserId();

        // 2. 在执行业务方法【之前】，获取“操作前状态”
        AssetChangeApplication appBefore = applicationMapper.selectById(applicationId);
        String fromState = appBefore.getApplicationStatus();

        // 3. 执行原始的业务方法 (例如 approveApplication)
        Object result = joinPoint.proceed();

        // 4. 在执行业务方法【之后】，获取“操作后状态”
        AssetChangeApplication appAfter = applicationMapper.selectById(applicationId);
        String toState = appAfter.getApplicationStatus();

        // 5. 构建日志对象
        AssetChangeApplicationOperationRecord logR = AssetChangeApplicationOperationRecord.builder()
                .id(SnowFlake.getInstance().newId())
                .applicationId(applicationId)
                .operatorUserId(operatorUserId)
                .operatorName(operatorName)
                .operationTime(LocalDateTime.now())
                .nextUserId(nextUserId)
                .nextUserName(nextUserName)
                .operationType(extractOperationType(joinPoint)) // 从方法名提取操作类型
                .operationOpinion(operationOpinion)
                .operationExplanation(operationExplanation)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 6. 插入日志到数据库
        logMapper.insert(logR);
        log.info("插入操作日志：{},fromState:{},toState:{}", logR, fromState, toState);
        // 7. 返回原始方法的执行结果
        return result;
    }

    /**
     * 辅助方法，根据方法名生成一个友好的操作类型描述
     */
    private String extractOperationType(ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        if (methodName.startsWith("approve")) {
            return OperationType.APPROVAL.name();
        }
        if (methodName.startsWith("submitForApproval")) {
            return OperationType.SUBMIT_APPROVAL.name();
        }
        if (methodName.startsWith("execute")) {
            return OperationType.EXECUTE.name();
        }
        if (methodName.startsWith("submitAcceptance")) {
            return OperationType.SUBMIT_ACCEPTANCE.name();
        }
        if (methodName.startsWith("accept")) {
            return OperationType.ACCEPTANCE.name();
        }
        return OperationType.UNKNOWN.name();
    }
}
