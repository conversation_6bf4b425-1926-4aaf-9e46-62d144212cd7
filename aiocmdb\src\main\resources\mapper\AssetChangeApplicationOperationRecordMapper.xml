<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetchange.dao.AssetChangeApplicationOperationRecordMapper">

    <!-- 插入操作记录 -->
    <insert id="insert" parameterType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationOperationRecord">
        INSERT INTO asset_change_application_operation_record (
            id, applicationId, operatorUserId, operatorName, operationTime, nextUserId, 
            nextUserName, operationType, operationOpinion, operationExplanation, createTime, updateTime
        ) VALUES (
            #{id}, #{applicationId}, #{operatorUserId}, #{operatorName}, #{operationTime}, #{nextUserId},
            #{nextUserName}, #{operationType}, #{operationOpinion}, #{operationExplanation}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入操作记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO asset_change_application_operation_record (
            id, applicationId, operatorUserId, operatorName, operationTime, nextUserId, 
            nextUserName, operationType, operationOpinion, operationExplanation, createTime, updateTime
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.applicationId}, #{item.operatorUserId}, #{item.operatorName}, #{item.operationTime}, 
             #{item.nextUserId}, #{item.nextUserName}, #{item.operationType}, #{item.operationOpinion}, 
             #{item.operationExplanation}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据申请单ID删除操作记录 -->
    <delete id="deleteByApplicationId" parameterType="long">
        DELETE FROM asset_change_application_operation_record WHERE applicationId = #{applicationId}
    </delete>

    <!-- 根据申请单ID查询操作记录 -->
    <select id="selectByApplicationId" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationOperationRecord">
        SELECT * FROM asset_change_application_operation_record 
        WHERE applicationId = #{applicationId}
        ORDER BY operationTime ASC
    </select>

    <!-- 根据申请单ID查询操作记录（按时间倒序） -->
    <select id="selectByApplicationIdOrderByTimeDesc" parameterType="long" resultType="com.digiwin.escloud.aiocmdb.assetchange.model.AssetChangeApplicationOperationRecord">
        SELECT * FROM asset_change_application_operation_record 
        WHERE applicationId = #{applicationId}
        ORDER BY operationTime DESC
    </select>

</mapper>
